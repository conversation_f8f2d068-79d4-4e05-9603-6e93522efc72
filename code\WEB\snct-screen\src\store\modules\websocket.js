import Vue from 'vue'

// 消息代码映射表
const MESSAGE_CODE_MAP = {
  '0A01': 'companyList',      // 企业列表
  '0A02': 'statistics',       // 统计数据
  '0A03': 'deviceInfo',       // 设备信息
  '0A04': 'alarmData',        // 报警数据
  '0B01': 'shipList',         // 船舶列表
  '0B02': 'shipStatus',       // 船舶状态
  '0B03': 'shipAlarms',       // 船舶报警
  '0B04': 'shipTracks',       // 船舶轨迹
  '0B05': 'shipWeather',      // 船舶气象
  '0B06': 'shipEquipment',    // 船舶设备
  '0C01': 'weatherData1',     // 气象数据1
  '0C02': 'weatherData2',     // 气象数据2
  '0D01': 'deviceData1',      // 设备数据1
  '0D02': 'deviceData2',      // 设备数据2
  '0E01': 'extraData1',       // 扩展数据1
  '0E02': 'extraData2',       // 扩展数据2
  '0E03': 'extraData3'        // 扩展数据3
}

// 反向映射：数据键到消息代码
const DATA_KEY_TO_CODE = Object.fromEntries(
  Object.entries(MESSAGE_CODE_MAP).map(([code, key]) => [key, code])
)

const state = {
  // 连接状态管理
  connectionStatus: 'disconnected', // disconnected, connecting, connected, error
  reconnectAttempts: 0,
  maxReconnectAttempts: 10,
  isManualClose: false,
  
  // 数据存储 - 使用语义化的键名
  data: {
    companyList: null,      // 企业列表 (原D0A01)
    statistics: null,       // 统计数据 (原D0A02)
    deviceInfo: null,       // 设备信息 (原D0A03)
    alarmData: null,        // 报警数据 (原D0A04)
    shipList: null,         // 船舶列表 (原D0B01)
    shipStatus: null,       // 船舶状态 (原D0B02)
    shipAlarms: null,       // 船舶报警 (原D0B03)
    shipTracks: null,       // 船舶轨迹 (原D0B04)
    shipWeather: null,      // 船舶气象 (原D0B05)
    shipEquipment: null,    // 船舶设备 (原D0B06)
    weatherData1: null,     // 气象数据1 (原D0C01)
    weatherData2: null,     // 气象数据2 (原D0C02)
    deviceData1: null,      // 设备数据1 (原D0D01)
    deviceData2: null,      // 设备数据2 (原D0D02)
    extraData1: null,       // 扩展数据1 (原D0E01)
    extraData2: null,       // 扩展数据2 (原D0E02)
    extraData3: null        // 扩展数据3 (原D0E03)
  },
  
  // 订阅管理
  activeSubscriptions: new Map(), // componentId -> { dataKeys: [], timestamp: number }
  currentDeptId: '100',
  currentSendText: '',
  
  // 元数据
  lastUpdateTime: {},
  dataVersion: {},
  
  // 应用配置
  title: '智慧船情可视化平台'
}

const getters = {
  // 连接状态
  isConnected: state => state.connectionStatus === 'connected',
  isConnecting: state => state.connectionStatus === 'connecting',
  isDisconnected: state => state.connectionStatus === 'disconnected',
  hasError: state => state.connectionStatus === 'error',
  
  // 数据获取器
  companyList: state => state.data.companyList,
  statistics: state => state.data.statistics,
  deviceInfo: state => state.data.deviceInfo,
  alarmData: state => state.data.alarmData,
  shipList: state => state.data.shipList,
  shipStatus: state => state.data.shipStatus,
  shipAlarms: state => state.data.shipAlarms,
  shipTracks: state => state.data.shipTracks,
  shipWeather: state => state.data.shipWeather,
  shipEquipment: state => state.data.shipEquipment,
  
  // 数据新鲜度检查
  isDataFresh: state => (dataKey, maxAge = 30000) => {
    const lastUpdate = state.lastUpdateTime[dataKey]
    return lastUpdate && (Date.now() - lastUpdate) < maxAge
  },
  
  // 获取组件订阅的数据
  getSubscriptionData: state => componentId => {
    const subscription = state.activeSubscriptions.get(componentId)
    if (!subscription) return {}
    
    return subscription.dataKeys.reduce((result, key) => {
      result[key] = state.data[key]
      return result
    }, {})
  },
  
  // 获取所有活跃的数据键
  getAllActiveDataKeys: state => {
    const allKeys = new Set()
    state.activeSubscriptions.forEach(subscription => {
      subscription.dataKeys.forEach(key => allKeys.add(key))
    })
    return Array.from(allKeys)
  },
  
  // 统计信息
  subscriptionCount: state => state.activeSubscriptions.size,
  
  // 连接信息摘要
  connectionSummary: state => ({
    status: state.connectionStatus,
    reconnectAttempts: state.reconnectAttempts,
    subscriptions: state.activeSubscriptions.size,
    lastDataUpdate: Math.max(...Object.values(state.lastUpdateTime), 0)
  })
}

const mutations = {
  // 连接状态管理
  SET_CONNECTION_STATUS(state, status) {
    state.connectionStatus = status
    console.log(`WebSocket连接状态变更: ${status}`)
  },
  
  SET_RECONNECT_ATTEMPTS(state, attempts) {
    state.reconnectAttempts = attempts
  },
  
  SET_MANUAL_CLOSE(state, isManual) {
    state.isManualClose = isManual
  },
  
  // 数据更新
  SET_WS_DATA(state, { key, data }) {
    Vue.set(state.data, key, data)
    Vue.set(state.lastUpdateTime, key, Date.now())
    
    // 数据版本控制
    const currentVersion = state.dataVersion[key] || 0
    Vue.set(state.dataVersion, key, currentVersion + 1)
    
    console.log(`WebSocket数据更新: ${key}`, data)
  },
  
  // 订阅管理
  ADD_SUBSCRIPTION(state, { componentId, dataKeys }) {
    state.activeSubscriptions.set(componentId, {
      dataKeys: [...dataKeys],
      timestamp: Date.now()
    })
    console.log(`组件订阅: ${componentId} -> [${dataKeys.join(', ')}]`)
  },
  
  REMOVE_SUBSCRIPTION(state, componentId) {
    if (state.activeSubscriptions.has(componentId)) {
      state.activeSubscriptions.delete(componentId)
      console.log(`取消订阅: ${componentId}`)
    }
  },
  
  SET_DEPT_ID(state, deptId) {
    state.currentDeptId = deptId
  },
  
  SET_SEND_TEXT(state, sendText) {
    state.currentSendText = sendText
  },
  
  // 清空数据
  CLEAR_ALL_DATA(state) {
    Object.keys(state.data).forEach(key => {
      Vue.set(state.data, key, null)
    })
    state.lastUpdateTime = {}
    state.dataVersion = {}
    console.log('WebSocket数据已清空')
  },
  
  CLEAR_SUBSCRIPTIONS(state) {
    state.activeSubscriptions.clear()
    console.log('所有订阅已清空')
  }
}

const actions = {
  // 处理WebSocket消息
  handleMessage({ commit }, messageData) {
    try {
      const { code, data } = JSON.parse(messageData)
      const dataKey = MESSAGE_CODE_MAP[code]

      if (dataKey) {
        commit('SET_WS_DATA', { key: dataKey, data })
      } else if (code === '0009') {
        // 处理认证失败
        console.error('WebSocket认证失败')
        commit('SET_CONNECTION_STATUS', 'error')
        // 这里可以触发重新登录逻辑
      } else {
        console.warn(`未知的消息代码: ${code}`)
      }
    } catch (error) {
      console.error('WebSocket消息解析失败:', error, messageData)
    }
  },

  // 订阅数据
  async subscribeData({ commit, state, dispatch }, { componentId, dataKeys, deptId }) {
    // 更新订阅记录
    commit('ADD_SUBSCRIPTION', { componentId, dataKeys })

    if (deptId) {
      commit('SET_DEPT_ID', deptId)
    }

    // 如果连接已建立，立即发送订阅请求
    if (state.connectionStatus === 'connected') {
      await dispatch('sendSubscriptionRequest')
    }
  },

  // 取消订阅
  async unsubscribeData({ commit, dispatch }, componentId) {
    commit('REMOVE_SUBSCRIPTION', componentId)
    // 重新优化订阅
    await dispatch('optimizeSubscriptions')
  },

  // 发送订阅请求
  async sendSubscriptionRequest({ state, commit }) {
    const allDataKeys = []
    state.activeSubscriptions.forEach(subscription => {
      subscription.dataKeys.forEach(key => {
        if (!allDataKeys.includes(key)) {
          allDataKeys.push(key)
        }
      })
    })

    if (allDataKeys.length > 0) {
      // 将数据键转换为消息代码
      const codes = allDataKeys.map(key => DATA_KEY_TO_CODE[key]).filter(Boolean)
      const sendText = `type66#${state.currentDeptId}#0#${codes.join(',')}`

      commit('SET_SEND_TEXT', sendText)

      // 这里需要调用实际的WebSocket发送方法
      // 我们将在重构webSocket.js时实现
      console.log('发送订阅请求:', sendText)

      // 通知WebSocket工具发送消息
      if (typeof window !== 'undefined' && window.webSocketManager) {
        window.webSocketManager.sendMessage(sendText)
      }
    }
  },

  // 优化订阅（去重并重新发送）
  async optimizeSubscriptions({ dispatch }) {
    await dispatch('sendSubscriptionRequest')
  },

  // 切换企业
  async switchCompany({ commit, dispatch }, { deptId, dataKeys }) {
    commit('SET_DEPT_ID', deptId)
    commit('CLEAR_ALL_DATA') // 清空旧数据

    // 如果提供了新的数据键，更新订阅
    if (dataKeys && dataKeys.length > 0) {
      // 这里假设是主要组件的切换，使用固定的componentId
      await dispatch('subscribeData', {
        componentId: 'main-component',
        dataKeys,
        deptId
      })
    } else {
      // 否则重新发送现有订阅
      await dispatch('sendSubscriptionRequest')
    }
  },

  // 连接状态更新
  updateConnectionStatus({ commit }, status) {
    commit('SET_CONNECTION_STATUS', status)
  },

  // 连接成功后的处理
  async onConnectionOpen({ commit, dispatch }) {
    commit('SET_CONNECTION_STATUS', 'connected')
    commit('SET_RECONNECT_ATTEMPTS', 0)

    // 重新发送所有订阅
    await dispatch('sendSubscriptionRequest')
  },

  // 连接关闭后的处理
  onConnectionClose({ commit, state }) {
    if (!state.isManualClose) {
      commit('SET_CONNECTION_STATUS', 'disconnected')
    }
  },

  // 连接错误处理
  onConnectionError({ commit }) {
    commit('SET_CONNECTION_STATUS', 'error')
  },

  // 手动关闭连接
  manualClose({ commit }) {
    commit('SET_MANUAL_CLOSE', true)
    commit('SET_CONNECTION_STATUS', 'disconnected')

    // 16秒后恢复自动重连
    setTimeout(() => {
      commit('SET_MANUAL_CLOSE', false)
    }, 16000)
  },

  // 清空所有数据和订阅
  clearAll({ commit }) {
    commit('CLEAR_ALL_DATA')
    commit('CLEAR_SUBSCRIPTIONS')
  }
}

// 导出常量供外部使用
export { MESSAGE_CODE_MAP, DATA_KEY_TO_CODE }

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
