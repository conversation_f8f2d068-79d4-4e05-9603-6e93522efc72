{"remainingRequest": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\indexs\\left-company-list.vue?vue&type=style&index=0&id=9dd02b40&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\indexs\\left-company-list.vue", "mtime": 1753842342235}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753075296083}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753075298360}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753075296703}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1753075295543}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753075297856}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["left-company-list.vue"], "names": [], "mappings": ";AA6HA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "left-company-list.vue", "sourceRoot": "src/views/indexs", "sourcesContent": ["<!--\n * @Author: weizw\n * @Date: 2025-07-28\n * @Description: 企业列表组件\n-->\n<template>\n  <div class=\"company-list-container\" v-if=\"pageflag\">\n    <div class=\"company-list beautify-scroll-def\">\n      <div\n        v-for=\"(company, index) in companyList\"\n        :key=\"company.deptId\"\n        class=\"company-item\"\n        :class=\"{ 'active': company.status === '1', 'inactive': company.status === '0' }\"\n        @click=\"handleCompanyClick(company)\"\n      >\n        <div class=\"company-index\">{{ index + 1 }}</div>\n        <div class=\"company-info\">\n          <div class=\"company-name\">{{ company.deptName }}</div>\n          <div class=\"company-status\">\n            <!-- <span class=\"status-dot\" :class=\"company.status === '1' ? 'online' : 'offline'\"></span> -->\n            <!-- <span class=\"status-text\">{{ company.status === '1' ? '在线' : '离线' }}</span> -->\n          </div>\n        </div>\n        <!-- <div class=\"company-id\">ID: {{ company.deptId }}</div> -->\n      </div>\n    </div>\n    \n    <!-- 无数据时显示 -->\n    <div v-if=\"companyList.length === 0\" class=\"no-data\">\n      <div class=\"no-data-text\">暂无企业数据</div>\n    </div>\n  </div>\n  \n  <!-- 数据加载失败时显示重新获取按钮 -->\n  <Reacquire v-else @onclick=\"handleReconnect\" line-height=\"200px\">\n    重新获取\n  </Reacquire>\n</template>\n\n<script>\nimport { dataModule } from '@/utils/webSocket'\n\nexport default {\n  name: 'LeftCompanyList',\n  data() {\n    return {\n      companyList: [],\n      pageflag: true,\n      timer: null,\n      wsCheckTimer: null\n    }\n  },\n  created() {\n    this.startDataMonitoring()\n  },\n  mounted() {\n    this.checkWebSocketData()\n  },\n  beforeDestroy() {\n    this.clearTimers()\n  },\n  methods: {\n    startDataMonitoring() {\n      this.wsCheckTimer = setInterval(() => {\n        this.checkWebSocketData()\n      }, 1000)\n    },\n    \n    // 检查WebSocket数据\n    checkWebSocketData() {\n      if (dataModule.D0A01 && Array.isArray(dataModule.D0A01)) {\n        const newData = [...dataModule.D0A01]\n        if (JSON.stringify(newData) !== JSON.stringify(this.companyList)) {\n          this.companyList = newData\n          console.log('企业列表数据更新:', this.companyList)\n        }\n      }\n    },\n    \n    // 处理重新连接请求\n    handleReconnect() {\n      // 通知父组件重新初始化WebSocket连接\n      this.$emit('reconnect')\n      this.pageflag = true\n    },\n\n    // 处理企业点击事件\n    handleCompanyClick(company) {\n      console.log('点击企业:', company)\n\n      // 修改dataModule.sendtext\n      dataModule.sendtext = `type66#${company.deptId}#0#0B01,0B02,0B03,0B04,0B05,0B06`\n\n      // 将企业信息保存到sessionStorage（关闭浏览器后清除）\n      const companyInfo = {\n        deptId: company.deptId,\n        sendtext: dataModule.sendtext,\n        title: company.title,\n        timestamp: Date.now(),\n        fromPage: 'company-list'\n      }\n      sessionStorage.setItem('selectedCompany', JSON.stringify(companyInfo))\n\n      console.log('更新后的sendtext:', dataModule.sendtext)\n\n      // 跳转到企业首页\n      this.$router.push('/comindex')\n    },\n\n    // 清理定时器\n    clearTimers() {\n      if (this.timer) {\n        clearInterval(this.timer)\n        this.timer = null\n      }\n      if (this.wsCheckTimer) {\n        clearInterval(this.wsCheckTimer)\n        this.wsCheckTimer = null\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.company-list-container {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.company-list {\n  flex: 1;\n  overflow-y: auto;\n  padding: 10px 0;\n  \n  .company-item {\n    display: flex;\n    align-items: center;\n    padding: 12px 16px;\n    margin: 8px 2px;\n    border-radius: 6px;\n    background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(0, 234, 255, 0.05));\n    border: 1px solid rgba(0, 234, 255, 0.2);\n    transition: all 0.3s ease;\n    cursor: pointer;\n\n    &:hover {\n      background: linear-gradient(135deg, rgba(0, 123, 255, 0.2), rgba(0, 234, 255, 0.1));\n      border-color: rgba(0, 234, 255, 0.4);\n      transform: translateX(2px);\n      box-shadow: 0 4px 12px rgba(0, 234, 255, 0.3);\n    }\n    \n    &.active {\n      border-color: rgba(7, 247, 168, 0.4);\n      background: linear-gradient(135deg, rgba(7, 247, 168, 0.1), rgba(7, 247, 168, 0.05));\n    }\n    \n    &.inactive {\n      border-color: rgba(227, 179, 55, 0.4);\n      background: linear-gradient(135deg, rgba(227, 179, 55, 0.1), rgba(227, 179, 55, 0.05));\n    }\n  }\n  \n  .company-index {\n    width: 30px;\n    height: 30px;\n    border-radius: 50%;\n    background: linear-gradient(135deg, #0072ff, #00eaff);\n    color: white;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-size: 14px;\n    font-weight: bold;\n    margin-right: 12px;\n    flex-shrink: 0;\n  }\n  \n  .company-info {\n    flex: 1;\n    min-width: 0;\n    \n    .company-name {\n      color: #ffffff;\n      font-size: 16px;\n      font-weight: 500;\n      margin-bottom: 4px;\n      white-space: nowrap;\n      overflow: hidden;\n      text-overflow: ellipsis;\n    }\n    \n    .company-status {\n      display: flex;\n      align-items: center;\n      \n      .status-dot {\n        width: 8px;\n        height: 8px;\n        border-radius: 50%;\n        margin-right: 6px;\n        \n        &.online {\n          background-color: #07f7a8;\n          box-shadow: 0 0 6px rgba(7, 247, 168, 0.6);\n        }\n        \n        &.offline {\n          background-color: #e3b337;\n          box-shadow: 0 0 6px rgba(227, 179, 55, 0.6);\n        }\n      }\n      \n      .status-text {\n        color: rgba(255, 255, 255, 0.8);\n        font-size: 12px;\n      }\n    }\n  }\n  \n  .company-id {\n    color: rgba(255, 255, 255, 0.6);\n    font-size: 12px;\n    flex-shrink: 0;\n  }\n}\n\n.no-data {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  \n  .no-data-text {\n    color: rgba(255, 255, 255, 0.6);\n    font-size: 16px;\n  }\n}\n\n// 滚动条样式\n.beautify-scroll-def {\n  &::-webkit-scrollbar {\n    width: 6px;\n  }\n  \n  &::-webkit-scrollbar-track {\n    background: rgba(255, 255, 255, 0.1);\n    border-radius: 3px;\n  }\n  \n  &::-webkit-scrollbar-thumb {\n    background: rgba(0, 234, 255, 0.4);\n    border-radius: 3px;\n    \n    &:hover {\n      background: rgba(0, 234, 255, 0.6);\n    }\n  }\n}\n</style>\n"]}]}