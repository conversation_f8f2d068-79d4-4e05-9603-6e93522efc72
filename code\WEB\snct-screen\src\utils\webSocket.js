import { removeToken } from '@/utils/auth'
import store from '@/store'

// WebSocket管理类
class WebSocketManager {
  constructor() {
    this.ws = null
    this.reconnectInterval = null
    this.heartbeatInterval = null
    this.heartbeatTimeout = null
    this.isDestroyed = false
  }

  // 初始化WebSocket连接
  init() {
    if (this.isDestroyed) {
      console.warn('WebSocketManager已销毁，无法初始化')
      return
    }

    // 清除之前的连接
    this.close()

    const token = localStorage.getItem('Admin-Token')
    if (!token) {
      console.error('登录失败：未获取到token')
      store.dispatch('websocket/updateConnectionStatus', 'error')
      return
    }

    try {
      store.dispatch('websocket/updateConnectionStatus', 'connecting')

      // 创建WebSocket连接
      this.ws = new WebSocket(`ws://127.0.0.1:8090/snct-visual/websocket/${token}`)

      this.setupEventHandlers()

    } catch (error) {
      console.error('WebSocket连接创建失败:', error)
      store.dispatch('websocket/onConnectionError')
    }
  }

  // 设置事件处理器
  setupEventHandlers() {
    if (!this.ws) return

    // 连接成功
    this.ws.onopen = () => {
      console.log('WebSocket连接成功')
      store.dispatch('websocket/onConnectionOpen')
      this.startHeartbeat()
    }

    // 接收消息
    this.ws.onmessage = (event) => {
      console.log('收到WebSocket消息:', event.data)

      try {
        // 重置心跳
        this.resetHeartbeat()

        // 处理消息
        store.dispatch('websocket/handleMessage', event.data)

      } catch (error) {
        console.error('消息处理失败:', error)
      }
    }

    // 连接关闭
    this.ws.onclose = (event) => {
      console.warn(`WebSocket连接关闭，代码: ${event.code}, 原因: ${event.reason}`)

      this.stopHeartbeat()
      store.dispatch('websocket/onConnectionClose')

      // 如果不是手动关闭且未销毁，尝试重连
      if (!store.state.websocket.isManualClose && !this.isDestroyed) {
        this.attemptReconnect()
      }
    }

    // 连接错误
    this.ws.onerror = (error) => {
      console.error('WebSocket错误:', error)
      store.dispatch('websocket/onConnectionError')
    }
  }

  // 发送消息
  sendMessage(message) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const token = localStorage.getItem('Admin-Token')
      if (!token) {
        console.warn('无法发送消息：token不存在')
        return false
      }

      this.ws.send(message)
      console.log('向服务器发送消息:', message)
      return true
    } else {
      console.warn('无法发送消息，WebSocket未连接')
      return false
    }
  }

  // 心跳机制
  startHeartbeat() {
    this.stopHeartbeat()

    // 每30秒发送一次心跳
    this.heartbeatInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send('ping')
      }
    }, 30000)

    // 设置心跳超时检测
    this.resetHeartbeat()
  }

  resetHeartbeat() {
    if (this.heartbeatTimeout) {
      clearTimeout(this.heartbeatTimeout)
    }

    // 60秒内没有收到消息则认为连接异常
    this.heartbeatTimeout = setTimeout(() => {
      console.warn('心跳超时，主动关闭连接')
      this.close()
    }, 60000)
  }

  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }

    if (this.heartbeatTimeout) {
      clearTimeout(this.heartbeatTimeout)
      this.heartbeatTimeout = null
    }
  }

  // 重连机制
  attemptReconnect() {
    const state = store.state.websocket

    if (state.reconnectAttempts >= state.maxReconnectAttempts) {
      console.error(`已达到最大重连次数 (${state.maxReconnectAttempts})，停止重连`)
      return
    }

    // 计算重连延迟（指数退避算法）
    const delay = Math.min(1000 * Math.pow(2, state.reconnectAttempts), 30000)

    console.log(`将在 ${delay} 毫秒后尝试第 ${state.reconnectAttempts + 1} 次重连...`)

    store.commit('websocket/SET_RECONNECT_ATTEMPTS', state.reconnectAttempts + 1)

    this.reconnectInterval = setTimeout(() => {
      if (!this.isDestroyed) {
        this.init()
      }
    }, delay)
  }

  // 手动关闭连接
  manualClose() {
    store.dispatch('websocket/manualClose')
    this.close()
  }

  // 关闭连接
  close() {
    this.stopHeartbeat()

    if (this.reconnectInterval) {
      clearTimeout(this.reconnectInterval)
      this.reconnectInterval = null
    }

    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }

  // 销毁管理器
  destroy() {
    this.isDestroyed = true
    this.close()
}

// 创建全局WebSocket管理器实例
const webSocketManager = new WebSocketManager()

// 将管理器挂载到window对象，供store使用
window.webSocketManager = webSocketManager

// 兼容性API - 保持向后兼容
export function initWebSocket() {
  console.log('使用新的WebSocket管理器初始化连接')
  if (typeof window !== 'undefined' && window.webSocketManager) {
    window.webSocketManager.init()
  } else {
    console.error('WebSocket管理器未初始化')
  }
}

export function sendMessage(message) {
  if (typeof window !== 'undefined' && window.webSocketManager) {
    return window.webSocketManager.sendMessage(message)
  }
  console.warn('WebSocket管理器未初始化')
  return false
}

export function manualClose() {
  if (typeof window !== 'undefined' && window.webSocketManager) {
    window.webSocketManager.manualClose()
  }
}

export function clearData() {
  store.dispatch('websocket/clearAll')
}

// 导出管理器实例（通过window对象访问）
export const getWebSocketManager = () => {
  return typeof window !== 'undefined' ? window.webSocketManager : null
}

// 为了向后兼容，保留dataModule的引用
// 但建议使用Vuex store替代
export const dataModule = {
  get title() { return store.state.websocket.title },
  get sendtext() { return store.state.websocket.currentSendText },
  set sendtext(value) { store.commit('websocket/SET_SEND_TEXT', value) },

  // 数据获取器 - 映射到Vuex store
  get D0A01() { return store.getters['websocket/companyList'] },
  get D0A02() { return store.getters['websocket/statistics'] },
  get D0A03() { return store.getters['websocket/deviceInfo'] },
  get D0A04() { return store.getters['websocket/alarmData'] },
  get D0B01() { return store.getters['websocket/shipList'] },
  get D0B02() { return store.state.websocket.data.shipStatus },
  get D0B03() { return store.state.websocket.data.shipAlarms },
  get D0B04() { return store.state.websocket.data.shipTracks },
  get D0B05() { return store.state.websocket.data.shipWeather },
  get D0B06() { return store.state.websocket.data.shipEquipment },
  get D0C01() { return store.state.websocket.data.weatherData1 },
  get D0C02() { return store.state.websocket.data.weatherData2 },
  get D0D01() { return store.state.websocket.data.deviceData1 },
  get D0D02() { return store.state.websocket.data.deviceData2 },
  get D0E01() { return store.state.websocket.data.extraData1 },
  get D0E02() { return store.state.websocket.data.extraData2 },
  get D0E03() { return store.state.websocket.data.extraData3 }
}




