<!--
 * @Author: daidai
 * @Date: 2022-03-04 09:23:59
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-05-07 11:05:02
 * @FilePath: \web-pc\src\pages\big-screen\view\indexs\index.vue
-->
<template>
  <div class="contents">
    <div class="contetn_left">
      <div class="pagetab">
        <!-- <div class="item">实时监测</div> -->
        
      </div>
      <ItemWrap class="contetn_left-company contetn_lr-item-large" title="企业列表">
        <LeftCompanyList />
      </ItemWrap>
      <ItemWrap
        class="contetn_left-bottom contetn_lr-item"
        title="设备提醒"
        style="padding: 0 10px 16px 10px"
      >
        <LeftBottom />
      </ItemWrap>
    </div>
    <div class="contetn_center">
      <CenterMap class="contetn_center_top" />
      <!--
      <ItemWrap class="contetn_center-bottom" title="安装计划">
        <CenterBottom />
      </ItemWrap>
      -->
    </div>
    <!--
    <div class="contetn_right">
      <ItemWrap
        class="contetn_left-bottom contetn_lr-item"
        title="报警次数"
      >
        <RightTop />
      </ItemWrap>
      <ItemWrap
        class="contetn_left-bottom contetn_lr-item"
        title="报警排名(TOP8)"
        style="padding: 0 10px 16px 10px"
      >
        <RightCenter />
      </ItemWrap>
      
      <ItemWrap
        class="contetn_left-bottom contetn_lr-item"
        title="数据统计图 "
      >
        <RightBottom />
      </ItemWrap>
    </div>
    -->
  </div>
</template>

<script>
import LeftCompanyList from './left-company-list.vue'
import LeftBottom from "./left-bottom.vue";
import CenterMap from "./center-map.vue";
import CenterBottom from "./center-bottom.vue";
import RightTop from "./right-top.vue";
import RightCenter from "./right-center.vue";
import RightBottom from "./right-bottom.vue";
import { websocketMixin } from '@/mixins/websocket-mixin'
import { initWebSocket, manualClose, clearData } from '@/utils/webSocket'

export default {
  mixins: [websocketMixin],

  components: {
    LeftCompanyList,
    LeftBottom,
    CenterMap,
    RightTop,
    RightCenter,
    RightBottom,
    CenterBottom,
  },

  data() {
    return {

    };
  },

  filters: {
    numsFilter(msg) {
      return msg || 0;
    },
  },

  async mounted() {
    // 统一初始化WebSocket连接，管理整个页面的数据获取
    await this.initWebSocketConnection()
  },

  methods: {
    // 统一初始化WebSocket连接
    async initWebSocketConnection() {
      try {
        // 清理旧连接和数据
        manualClose()
        clearData()

        // 初始化WebSocket连接
        initWebSocket()

        // 等待连接建立
        const connected = await this.$waitForConnection(10000)
        if (!connected) {
          console.error('WebSocket连接超时')
          return
        }

        // 订阅主页面需要的数据
        await this.$subscribeWsData([
          'companyList',    // 企业列表
          'statistics',     // 统计数据
          'deviceInfo',     // 设备信息
          'alarmData'       // 报警数据
        ], '100') // 默认部门ID

        console.log('主页面WebSocket连接已初始化并订阅数据')
      } catch (error) {
        console.error('主页面WebSocket连接失败:', error)
      }
    }
  },
};
</script>
<style lang="scss" scoped>
// 内容
.contents {
  .contetn_left,
  .contetn_right {
    width: 430px;
    box-sizing: border-box;
  }

  .contetn_left {
    height: 960px;
    gap: 10px;
  }

  .contetn_center {
    height: 960px;
    width: 1439px;
  }

  //左右两侧 三个块
  .contetn_lr-item {
    height: 310px;
  }

  // 企业列表区域
  .contetn_lr-item-large {
    height: 635px;
  }

  .contetn_center_top {
    width: 100%;
  }

  // 中间
  .contetn_center {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
  }

  .contetn_center-bottom {
    height: 315px;
  }

  //左边 右边 结构一样
  .contetn_left,
  .contetn_right {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    position: relative;
  }
}


@keyframes rotating {
    0% {
        -webkit-transform: rotate(0) scale(1);
        transform: rotate(0) scale(1);
    }
    50% {
        -webkit-transform: rotate(180deg) scale(1.1);
        transform: rotate(180deg) scale(1.1);
    }
    100% {
        -webkit-transform: rotate(360deg) scale(1);
        transform: rotate(360deg) scale(1);
    }
}
</style>
