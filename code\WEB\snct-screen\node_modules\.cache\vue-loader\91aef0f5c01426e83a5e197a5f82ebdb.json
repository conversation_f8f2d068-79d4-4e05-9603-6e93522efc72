{"remainingRequest": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\indexs\\center-map.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\indexs\\center-map.vue", "mtime": 1753842342937}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753075297856}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["center-map.vue"], "names": [], "mappings": ";AA2BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "center-map.vue", "sourceRoot": "src/views/indexs", "sourcesContent": ["<!--\r\n * @Author: daidai\r\n * @Date: 2022-03-01 11:17:39\r\n * @LastEditors: Please set LastEditors\r\n * @LastEditTime: 2022-09-29 15:50:18\r\n * @FilePath: \\web-pc\\src\\pages\\big-screen\\view\\indexs\\center-map.vue\r\n-->\r\n<template>\r\n  <div class=\"centermap\">\r\n    <div class=\"maptitle\">\r\n      <div class=\"zuo\"></div>\r\n      <span class=\"titletext\">{{ maptitle }}</span>\r\n      <div class=\"you\"></div>\r\n    </div>\r\n    <div class=\"mapwrap\">\r\n      <dv-border-box-13>\r\n        <div class=\"quanguo\" @click=\"getData('china')\" v-if=\"code !== 'china'\">\r\n          中国\r\n        </div>\r\n\r\n        <Echart id=\"CenterMap\" :options=\"options\" ref=\"CenterMap\" />\r\n      </dv-border-box-13>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport xzqCode from \"../../utils/map/xzqCode\";\r\nimport { currentGET } from \"api/modules\";\r\nimport * as echarts from \"echarts\";\r\nimport { GETNOBASE } from \"api\";\r\nimport { dataModule } from '@/utils/webSocket';\r\nexport default {\r\n  data() {\r\n    return {\r\n      maptitle: \"接入船总数量：\",\r\n      options: {},\r\n      code: \"china\", //china 代表中国 其他地市是行政编码\r\n      echartBindClick: false,\r\n      isSouthChinaSea: false, //是否要展示南海群岛  修改此值请刷新页面\r\n      wsCheckTimer: null, // WebSocket数据监听定时器\r\n      shipTotalCount: 0, // 船舶总数量\r\n      enterpriseTotalCount: 0, // 企业总数量\r\n    };\r\n  },\r\n  created() {},\r\n\r\n  mounted() {\r\n    // console.log(xzqCode);\r\n    this.getData(\"china\");\r\n    this.startDataMonitoring();\r\n  },\r\n  beforeDestroy() {\r\n    // 清除定时器\r\n    if (this.wsCheckTimer) {\r\n      clearInterval(this.wsCheckTimer);\r\n      this.wsCheckTimer = null;\r\n    }\r\n  },\r\n  methods: {\r\n    // 开始WebSocket数据监听\r\n    startDataMonitoring() {\r\n      this.wsCheckTimer = setInterval(() => {\r\n        this.checkWebSocketData();\r\n      }, 1000);\r\n    },\r\n\r\n    // 检查WebSocket数据\r\n    checkWebSocketData() {\r\n      if (dataModule.D0A02) {\r\n        const newData = dataModule.D0A02;\r\n        // 检查数据是否有变化\r\n        if (newData.ship_num !== this.shipTotalCount || newData.enterprise_num !== this.enterpriseTotalCount) {\r\n          this.shipTotalCount = newData.ship_num || 0;\r\n          this.enterpriseTotalCount = newData.enterprise_num || 0;\r\n\r\n          // 更新地图标题显示\r\n          this.updateMapTitle();\r\n\r\n          console.log('船舶数量数据更新:', {\r\n            ship_num: this.shipTotalCount,\r\n            enterprise_num: this.enterpriseTotalCount\r\n          });\r\n        }\r\n      }\r\n    },\r\n\r\n    // 更新地图标题\r\n    updateMapTitle() {\r\n      this.maptitle = `企业数量：${this.enterpriseTotalCount} 家 | 接入船总数量：${this.shipTotalCount} 艘`;\r\n    },\r\n\r\n    getData(code) {\r\n      currentGET(\"big8\", { regionCode: code }).then((res) => {\r\n        console.log(\"设备分布\", res);\r\n        if (res.success) {\r\n          this.getGeojson(res.data.regionCode, res.data.dataList);\r\n          this.mapclick();\r\n        } else {\r\n          this.$Message.warning(res.msg);\r\n        }\r\n      });\r\n    },\r\n    /**\r\n     * @description: 获取geojson\r\n     * @param {*} name china 表示中国 其他省份行政区编码\r\n     * @param {*} mydata 接口返回列表数据\r\n     * @return {*}\r\n     */\r\n    async getGeojson(name, mydata) {\r\n      this.code = name;\r\n      //如果要展示南海群岛并且展示的是中国的话\r\n      let geoname=name\r\n      if (this.isSouthChinaSea && name == \"china\") {\r\n        geoname = \"chinaNanhai\";\r\n      }\r\n      //如果有注册地图的话就不用再注册 了\r\n      let mapjson = echarts.getMap(name);\r\n      if (mapjson) {\r\n        mapjson = mapjson.geoJSON;\r\n      } else {\r\n        mapjson = await GETNOBASE(`./map-geojson/${geoname}.json`).then((res) => {\r\n          return res;\r\n        });\r\n        echarts.registerMap(name, mapjson);\r\n      }\r\n      let cityCenter = {};\r\n      let arr = mapjson.features;\r\n      //根据geojson获取省份中心点\r\n      arr.map((item) => {\r\n        cityCenter[item.properties.name] =\r\n          item.properties.centroid || item.properties.center;\r\n      });\r\n      let newData = [];\r\n      mydata.map((item) => {\r\n        if (cityCenter[item.name]) {\r\n          newData.push({\r\n            name: item.name,\r\n            value: cityCenter[item.name].concat(item.value),\r\n          });\r\n        }\r\n      });\r\n      this.init(name, mydata, newData);\r\n    },\r\n    init(name, data, data2) {\r\n      // console.log(data2);\r\n      let top = 45;\r\n      let zoom = 1.05;\r\n      let option = {\r\n        backgroundColor: \"rgba(0,0,0,0)\",\r\n        tooltip: {\r\n          show: false,\r\n        },\r\n        legend: {\r\n          show: false,\r\n        },\r\n        visualMap: {\r\n          left: 20,\r\n          bottom: 20,\r\n          pieces: [\r\n            { gte: 1000, label: \"1000个以上\" }, // 不指定 max，表示 max 为无限大（Infinity）。\r\n            { gte: 600, lte: 999, label: \"600-999个\" },\r\n            { gte: 200, lte: 599, label: \"200-599个\" },\r\n            { gte: 50, lte: 199, label: \"49-199个\" },\r\n            { gte: 10, lte: 49, label: \"10-49个\" },\r\n            { lte: 9, label: \"1-9个\" }, // 不指定 min，表示 min 为无限大（-Infinity）。\r\n          ],\r\n          inRange: {\r\n            // 渐变颜色，从小到大\r\n            color: [\r\n              \"#c3d7df\",\r\n              \"#5cb3cc\",\r\n              \"#8abcd1\",\r\n              \"#66a9c9\",\r\n              \"#2f90b9\",\r\n              \"#1781b5\",\r\n            ],\r\n          },\r\n          textStyle: {\r\n            color: \"#fff\",\r\n          },\r\n        },\r\n        geo: {\r\n          map: name,\r\n          roam: false,\r\n          selectedMode: false, //是否允许选中多个区域\r\n          zoom: zoom,\r\n          top: top,\r\n          // aspectScale: 0.78,\r\n          show: false,\r\n        },\r\n        series: [\r\n          {\r\n            name: \"MAP\",\r\n            type: \"map\",\r\n            map: name,\r\n            // aspectScale: 0.78,\r\n            data: data,\r\n            // data: [1,100],\r\n            selectedMode: false, //是否允许选中多个区域\r\n            zoom: zoom,\r\n            geoIndex: 1,\r\n            top: top,\r\n            tooltip: {\r\n              show: true,\r\n              formatter: function (params) {\r\n                if (params.data) {\r\n                  return params.name + \"：\" + params.data[\"value\"];\r\n                } else {\r\n                  return params.name;\r\n                }\r\n              },\r\n              backgroundColor: \"rgba(0,0,0,.6)\",\r\n              borderColor: \"rgba(147, 235, 248, .8)\",\r\n              textStyle: {\r\n                color: \"#FFF\",\r\n              },\r\n            },\r\n            label: {\r\n              show: false,\r\n              color: \"#000\",\r\n              // position: [-10, 0],\r\n              formatter: function (val) {\r\n                // console.log(val)\r\n                if (val.data !== undefined) {\r\n                  return val.name.slice(0, 2);\r\n                } else {\r\n                  return \"\";\r\n                }\r\n              },\r\n              rich: {},\r\n            },\r\n            emphasis: {\r\n              label: {\r\n                show: false,\r\n              },\r\n              itemStyle: {\r\n                areaColor: \"#389BB7\",\r\n                borderWidth: 1,\r\n              },\r\n            },\r\n            itemStyle: {\r\n              borderColor: \"rgba(147, 235, 248, .8)\",\r\n              borderWidth: 1,\r\n              areaColor: {\r\n                type: \"radial\",\r\n                x: 0.5,\r\n                y: 0.5,\r\n                r: 0.8,\r\n                colorStops: [\r\n                  {\r\n                    offset: 0,\r\n                    color: \"rgba(147, 235, 248, 0)\", // 0% 处的颜色\r\n                  },\r\n                  {\r\n                    offset: 1,\r\n                    color: \"rgba(147, 235, 248, .2)\", // 100% 处的颜色\r\n                  },\r\n                ],\r\n                globalCoord: false, // 缺为 false\r\n              },\r\n              shadowColor: \"rgba(128, 217, 248, .3)\",\r\n              shadowOffsetX: -2,\r\n              shadowOffsetY: 2,\r\n              shadowBlur: 10,\r\n            },\r\n          },\r\n          {\r\n            data: data2,\r\n            type: \"effectScatter\",\r\n            coordinateSystem: \"geo\",\r\n            symbolSize: function (val) {\r\n              return 4;\r\n              // return val[2] / 50;\r\n            },\r\n            legendHoverLink: true,\r\n            showEffectOn: \"render\",\r\n            rippleEffect: {\r\n              // period: 4,\r\n              scale: 6,\r\n              color: \"rgba(255,255,255, 1)\",\r\n              brushType: \"fill\",\r\n            },\r\n            tooltip: {\r\n              show: true,\r\n              formatter: function (params) {\r\n                if (params.data) {\r\n                  return params.name + \"：\" + params.data[\"value\"][2];\r\n                } else {\r\n                  return params.name;\r\n                }\r\n              },\r\n              backgroundColor: \"rgba(0,0,0,.6)\",\r\n              borderColor: \"rgba(147, 235, 248, .8)\",\r\n              textStyle: {\r\n                color: \"#FFF\",\r\n              },\r\n            },\r\n            label: {\r\n              formatter: (param) => {\r\n                return param.name.slice(0, 2);\r\n              },\r\n\r\n              fontSize: 11,\r\n              offset: [0, 2],\r\n              position: \"bottom\",\r\n              textBorderColor: \"#fff\",\r\n              textShadowColor: \"#000\",\r\n              textShadowBlur: 10,\r\n              textBorderWidth: 0,\r\n              color: \"#FFF\",\r\n              show: true,\r\n            },\r\n            // colorBy: \"data\",\r\n            itemStyle: {\r\n              color: \"rgba(255,255,255,1)\",\r\n              borderColor: \"rgba(2255,255,255,2)\",\r\n              borderWidth: 4,\r\n              shadowColor: \"#000\",\r\n              shadowBlur: 10,\r\n            },\r\n          },\r\n        ],\r\n         //动画效果\r\n            // animationDuration: 1000,\r\n            // animationEasing: 'linear',\r\n            // animationDurationUpdate: 1000\r\n      };\r\n      this.options = option;\r\n    },\r\n    message(text) {\r\n      this.$Message({\r\n        text: text,\r\n        type: \"warning\",\r\n      });\r\n    },\r\n    mapclick() {\r\n      if (this.echartBindClick) return;\r\n      //单击切换到级地图，当mapCode有值,说明可以切换到下级地图\r\n      this.$refs.CenterMap.chart.on(\"click\", (params) => {\r\n        // console.log(params);\r\n        let xzqData = xzqCode[params.name];\r\n        if (xzqData) {\r\n          this.getData(xzqData.adcode);\r\n        } else {\r\n          this.message(\"暂无下级地市!\");\r\n        }\r\n      });\r\n      this.echartBindClick = true;\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.centermap {\r\n  .maptitle {\r\n    display: flex;\r\n    justify-content: right;\r\n    margin: 25px 0 5px 0;\r\n    box-sizing: border-box;\r\n\r\n    .titletext {\r\n      font-size: 26px;\r\n      font-weight: 300;\r\n      letter-spacing: 1px;\r\n      background: linear-gradient(\r\n        92deg,\r\n        #0072ff 0%,\r\n        #00eaff 48.8525390625%,\r\n        #01aaff 100%\r\n      );\r\n      -webkit-background-clip: text;\r\n      -webkit-text-fill-color: transparent;\r\n      margin: 0 16px;\r\n    }\r\n\r\n    .zuo,\r\n    .you {\r\n      background-size: 100% 100%;\r\n      width: 26px;\r\n      height: 16px;\r\n      margin-top: 7px;\r\n    }\r\n\r\n    .zuo {\r\n      background: url(\"../../assets/img/xiezuo.png\") no-repeat;\r\n    }\r\n\r\n    .you {\r\n      background: url(\"../../assets/img/xieyou.png\") no-repeat;\r\n    }\r\n  }\r\n\r\n  .mapwrap {\r\n    height: 900px;\r\n    width: 100%;\r\n    // padding: 0 0 10px 0;\r\n    box-sizing: border-box;\r\n    position: relative;\r\n\r\n    .quanguo {\r\n      position: absolute;\r\n      right: 20px;\r\n      top: -46px;\r\n      width: 80px;\r\n      height: 28px;\r\n      border: 1px solid #00eded;\r\n      border-radius: 10px;\r\n      color: #00f7f6;\r\n      text-align: center;\r\n      line-height: 26px;\r\n      letter-spacing: 6px;\r\n      cursor: pointer;\r\n      box-shadow: 0 2px 4px rgba(0, 237, 237, 0.5),\r\n        0 0 6px rgba(0, 237, 237, 0.4);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}