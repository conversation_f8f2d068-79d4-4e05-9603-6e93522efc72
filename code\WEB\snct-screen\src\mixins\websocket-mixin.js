import { mapState, mapGetters, mapActions } from 'vuex'

/**
 * WebSocket数据订阅Mixin
 * 提供组件级别的WebSocket数据订阅和管理功能
 */
export const websocketMixin = {
  computed: {
    // WebSocket连接状态
    ...mapState('websocket', ['connectionStatus']),
    ...mapGetters('websocket', ['isConnected', 'isConnecting', 'hasError']),
    
    // 快捷访问常用数据
    wsCompanyList() {
      return this.$store.getters['websocket/companyList']
    },
    
    wsStatistics() {
      return this.$store.getters['websocket/statistics']
    },
    
    wsShipList() {
      return this.$store.getters['websocket/shipList']
    },
    
    // 连接状态文本
    connectionStatusText() {
      const statusMap = {
        'disconnected': '未连接',
        'connecting': '连接中...',
        'connected': '已连接',
        'error': '连接错误'
      }
      return statusMap[this.connectionStatus] || '未知状态'
    }
  },
  
  methods: {
    ...mapActions('websocket', [
      'subscribeData',
      'unsubscribeData',
      'switchCompany',
      'updateConnectionStatus'
    ]),
    
    /**
     * 订阅WebSocket数据
     * @param {string|Array} dataKeys - 要订阅的数据键
     * @param {string} deptId - 部门ID（可选）
     * @returns {Promise}
     */
    async $subscribeWsData(dataKeys, deptId = null) {
      const keys = Array.isArray(dataKeys) ? dataKeys : [dataKeys]
      const componentId = this.$options.name || this._uid.toString()
      
      try {
        await this.subscribeData({
          componentId,
          dataKeys: keys,
          deptId
        })
        
        console.log(`组件 ${componentId} 订阅数据:`, keys)
        return true
      } catch (error) {
        console.error(`组件 ${componentId} 订阅数据失败:`, error)
        return false
      }
    },
    
    /**
     * 取消WebSocket数据订阅
     * @returns {Promise}
     */
    async $unsubscribeWsData() {
      const componentId = this.$options.name || this._uid.toString()
      
      try {
        await this.unsubscribeData(componentId)
        console.log(`组件 ${componentId} 取消订阅`)
        return true
      } catch (error) {
        console.error(`组件 ${componentId} 取消订阅失败:`, error)
        return false
      }
    },
    
    /**
     * 获取WebSocket数据
     * @param {string} dataKey - 数据键
     * @returns {any} 数据值
     */
    $getWsData(dataKey) {
      return this.$store.state.websocket.data[dataKey]
    },
    
    /**
     * 检查数据是否新鲜
     * @param {string} dataKey - 数据键
     * @param {number} maxAge - 最大年龄（毫秒）
     * @returns {boolean}
     */
    $isWsDataFresh(dataKey, maxAge = 30000) {
      return this.$store.getters['websocket/isDataFresh'](dataKey, maxAge)
    },
    
    /**
     * 切换企业并订阅相关数据
     * @param {string} deptId - 企业ID
     * @param {Array} dataKeys - 要订阅的数据键
     * @returns {Promise}
     */
    async $switchCompany(deptId, dataKeys = []) {
      try {
        await this.switchCompany({ deptId, dataKeys })
        console.log(`切换到企业 ${deptId}，订阅数据:`, dataKeys)
        return true
      } catch (error) {
        console.error(`切换企业失败:`, error)
        return false
      }
    },
    
    /**
     * 等待连接建立
     * @param {number} timeout - 超时时间（毫秒）
     * @returns {Promise<boolean>}
     */
    async $waitForConnection(timeout = 10000) {
      return new Promise((resolve) => {
        if (this.isConnected) {
          resolve(true)
          return
        }
        
        const startTime = Date.now()
        const checkConnection = () => {
          if (this.isConnected) {
            resolve(true)
          } else if (Date.now() - startTime > timeout) {
            resolve(false)
          } else {
            setTimeout(checkConnection, 100)
          }
        }
        
        checkConnection()
      })
    }
  },
  
  // 自动清理订阅
  beforeDestroy() {
    this.$unsubscribeWsData()
  }
}

/**
 * 企业列表专用Mixin
 * 专门用于企业列表组件的数据订阅
 */
export const companyListMixin = {
  mixins: [websocketMixin],
  
  computed: {
    companyList() {
      return this.wsCompanyList || []
    }
  },
  
  async created() {
    await this.$subscribeWsData(['companyList'])
  },
  
  methods: {
    async handleCompanyClick(company) {
      // 保存企业信息到sessionStorage
      const companyInfo = {
        deptId: company.deptId,
        deptName: company.deptName,
        timestamp: Date.now(),
        fromPage: 'company-list'
      }
      sessionStorage.setItem('selectedCompany', JSON.stringify(companyInfo))
      
      // 切换企业并订阅企业相关数据
      await this.$switchCompany(company.deptId, [
        'shipList', 'shipStatus', 'shipAlarms', 
        'shipTracks', 'shipWeather', 'shipEquipment'
      ])
      
      console.log('企业切换完成:', company.deptName)
      
      // 跳转到企业详情页
      this.$router.push('/comindex')
    }
  }
}

/**
 * 地图组件专用Mixin
 * 专门用于地图组件的统计数据订阅
 */
export const mapMixin = {
  mixins: [websocketMixin],
  
  computed: {
    statistics() {
      return this.wsStatistics
    },
    
    mapTitle() {
      if (!this.statistics) {
        return '企业数量： 家 | 接入船总数量： 艘'
      }
      
      const { enterprise_num = 0, ship_num = 0 } = this.statistics
      return `企业数量：${enterprise_num} 家 | 接入船总数量：${ship_num} 艘`
    }
  },
  
  async created() {
    await this.$subscribeWsData(['statistics'])
  }
}

/**
 * 船舶相关组件专用Mixin
 * 专门用于船舶相关组件的数据订阅
 */
export const shipMixin = {
  mixins: [websocketMixin],
  
  computed: {
    shipList() {
      return this.wsShipList || []
    },
    
    shipStatus() {
      return this.$getWsData('shipStatus')
    },
    
    shipAlarms() {
      return this.$getWsData('shipAlarms')
    }
  },
  
  async created() {
    await this.$subscribeWsData(['shipList', 'shipStatus', 'shipAlarms'])
  }
}
